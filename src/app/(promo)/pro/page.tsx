"use client";

import Link from "next/link";
import { AppButton } from "../../components/app-button";
import Benefits from "../../components/BenefitsPro/BenefitsPro";
import VideoPlayer from "../../components/VideoPlayer/VideoPlayer";
import HowItWorks from "../../components/HowItWorks/HowItWorks";
import {
  AppVideoCarousel,
  PRO_VIDEOS,
} from "../../components/AppVideosCarousel/AppVideosCarousel";
import TestingStarters from "../../components/TestingStarters/TestingStarters";
import Builder from "../../components/Builder/Builder";
import Media from "../../components/Media/Media";
import Footer from "../../components/Footer/Footer";
import Navbar from "../../components/Navbar/Navbar";
import { GradientWrapper } from "../../components/gradient-wrapper";
import SubstackList from "../../components/Substack/Substack";
import CompanyLogos from "../lp-components/company-logos";
import TvlProtectedSection from "../lp-components/tvl-protected-section";
import Testimonials from "../../components/Testimonials/Testimonials";
import TrophiesSection from "../lp-components/trophies-section";
import TeamSection from "../lp-components/team-section";
import ServicesSection from "../lp-components/services-section";

export default function Home() {
  return (
    <div>
      <Navbar />
      <div className="main-container w-full overflow-x-hidden">
        <div className="absolute inset-0 z-0 bg-gradient-to-b from-transparent via-transparent to-black">
          <GradientWrapper />
        </div>
        <main className="relative z-10">
          <section className="mx-auto mb-5 flex w-[87.5%] flex-col items-center justify-center pt-[80px] lg:w-4/5 lg:pt-[180px]">
            <h1 className="main-title-custom mb-5 text-center text-[56px] font-bold leading-[48px] tracking-normal lg:text-[120px] lg:leading-[100px]">
              INVARIANT TESTING IN THE CLOUD
            </h1>
            <h2 className="mb-5 text-center font-thin tracking-normal text-white lg:my-[37px] lg:text-[37px] lg:leading-[33px]">
              Run Stateful Fuzz tests and Formal Verification tools in 3 clicks
            </h2>
            <Link
              href="https://t.me/GalloDaSballo"
              className="m-0 flex flex-row items-center justify-center p-0 text-center"
              target="_blank"
              rel="noopener noreferrer"
            >
              <AppButton variant="primary" size="lg">
                Use Recon Pro Now
              </AppButton>
            </Link>

            <VideoPlayer
              link={"https://www.youtube.com/embed/bVN6TmdfTF4"}
              overlayText={"Watch the 1 minute intro"}
            />

            <CompanyLogos />
          </section>

          <TvlProtectedSection />

          <section
            id="benefits"
            className="mx-auto mb-5 flex w-[87.5%] flex-col items-center justify-center pt-[30px] lg:pt-[180px]"
          >
            <h3 className=" sub-title-custom text-center font-bold uppercase leading-[48px] tracking-normal lg:text-[100px] lg:leading-[100px]">
              Benefits
            </h3>
            <Benefits />
          </section>

          <Testimonials />

          <section className="mx-auto mb-5 flex w-[87.5%] flex-col items-center justify-center pt-[30px] lg:w-4/5 lg:pt-[180px]">
            <h3 className=" sub-title-custom text-center font-bold uppercase leading-[48px] tracking-normal lg:text-[100px] lg:leading-[100px]">
              How does it work
            </h3>
            <h2 className="mb-5 text-center font-thin tracking-normal text-white lg:my-[37px] lg:text-[37px] lg:leading-[33px]">
              Installation is faster than finding your hardware wallet
            </h2>
            <Link href="/dashboard" className="m-0 w-[150px] p-0 text-center">
              <AppButton variant="secondary" className="m-0 p-0">
                Log in &gt;
              </AppButton>
            </Link>

            <HowItWorks />
          </section>

          <section
            id="demos"
            className="mx-auto mb-5 mt-[30px] flex w-full flex-col items-center justify-center bg-[#6750A41F] pt-[30px] lg:p-[30px]"
          >
            <AppVideoCarousel videos={PRO_VIDEOS} />
          </section>

          <TrophiesSection />

          <TeamSection />

          <ServicesSection />

          <section className="mx-auto mb-5 flex w-[87.5%] flex-col justify-center pt-[30px] lg:w-4/5 lg:pt-[180px]">
            <h3 className=" sub-title-custom w-[450px] items-start break-words font-bold uppercase leading-[48px] tracking-normal lg:w-[850px] lg:text-[100px] lg:leading-[100px]">
              Invariant testing starters
            </h3>
            <TestingStarters />
          </section>

          <section
            id="builder"
            className="mx-auto mb-5 flex w-[87.5%] flex-col justify-center pt-[30px] lg:w-4/5 lg:pt-[180px]"
          >
            <h3 className=" sub-title-custom mb-3 w-[700px] items-start font-bold uppercase leading-[48px] tracking-normal lg:text-[100px] lg:leading-[100px]">
              Recon builder
            </h3>
            <Builder />
          </section>

          <section
            id="media"
            className="mx-auto mb-5 flex w-[87.5%] flex-col items-center justify-center pt-[30px] lg:w-4/5 lg:pt-[180px]"
          >
            <div className="flex w-full flex-col items-center justify-between lg:flex-row">
              <h3 className=" sub-title-custom text-center font-bold uppercase leading-[48px] tracking-normal lg:text-[100px] lg:leading-[100px] ">
                Tutorials
              </h3>
              <Link href="/media" className="m-0 p-0">
                <AppButton variant="secondary" className="m-0 p-0">
                  View all videos &gt;
                </AppButton>
              </Link>
            </div>
            <Media short={true} />
          </section>
          <section
            id="substack"
            className="mx-auto mb-5 flex w-[87.5%] flex-col items-center justify-center pt-[30px] lg:w-4/5 lg:pt-[180px]"
          >
            <div className="mb-[40px] flex w-full flex-col items-center justify-between lg:flex-row">
              <h3 className=" sub-title-custom text-center font-bold uppercase leading-[48px] tracking-normal lg:text-[100px] lg:leading-[100px] ">
                Substack posts
              </h3>
              <Link
                href="https://getrecon.substack.com/"
                className="mb-5 text-center"
              >
                <AppButton variant="secondary" className="m-0 p-0">
                  Read more &gt;
                </AppButton>
              </Link>
            </div>
            <SubstackList />
          </section>
        </main>
        <div className="w-full border-t border-t-[#FFFFFF]">
          <Footer />
        </div>
      </div>
    </div>
  );
}
