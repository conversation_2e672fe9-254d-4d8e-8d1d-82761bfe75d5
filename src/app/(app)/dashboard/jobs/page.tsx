"use client";

import axios from "axios";
import { useState } from "react";

import { ENV_TYPE } from "@/app/app.constants";
import type { GitHubLinkFormValues } from "@/app/components/github-link-form/create-job-form";
import { CreateJobForm } from "@/app/components/github-link-form/create-job-form";
import { Webhooks } from "@/app/components/Webhooks";
import { useGetJobs } from "@/app/services/jobs.hooks";

import { AllJobs } from "./all-jobs";
import { checkFields } from "@/utils/fieldChecker";
import { useGetMyOrg } from "@/app/services/organization.hooks";
import { Toaster, toast } from "react-hot-toast";
import Link from "next/link";
import config from "@/config";

export default function JobsPage() {
  const [env, setEnv] = useState(ENV_TYPE.MEDUSA);
  const [jobId, setJobId] = useState<null | number>(null);
  const [recipeId, setRecipeId] = useState<null | string>(null); // Track if the job was created from a recipe

  const { data: allJobs, refetch: refetchJobs } = useGetJobs();
  const { data: organization } = useGetMyOrg();

  const checkCanClone = async (repoName: string, orgName: string) => {
    try {
      await axios({
        method: "POST",
        url: `/api/jobs/canclone`,
        data: {
          orgName,
          repoName,
        },
      });
    } catch (e) {
      toast((t) => (
        <span className="flex w-[300px] flex-col items-center">
          Recon doesn't have access to this repository, please authorize the
          Recon app on the repo
          <Link href="/dashboard/installs" className="mt-2 text-black">
            Redirecting ...
          </Link>
        </span>
      ));
      setTimeout(() => {
        window.location.href = config.github.app.installationUrl;
      }, 3000);
      throw new Error("Can't clone this repo");
    }
  };

  const runningJobs = () => {
    return allJobs.filter(
      (job) =>
        job.status === "RUNNING" ||
        job.status === "STARTED" ||
        job.status === "QUEUED"
    );
  };
  const startEchidnaAbiJob = async ({
    pathToTester,
    echidnaConfig,
    contract,
    corpusDir,
    forkBlock,
    forkMode,
    forkReplacement,
    ref,
    repoName,
    rpcUrl,
    testLimit,
    testMode,
    preprocess,
    directory,
    orgName,
    targetCorpus,
    label,
  }: GitHubLinkFormValues) => {
    if (organization.billingStatus === "TRIAL" && runningJobs().length > 0) {
      alert(
        `You can only run one job at a time, currently running: ${runningJobs()
          .map((job) => `${job.label}-${job.id}`)
          .join(", ")}`
      );
      return;
    }
    try {
      await checkCanClone(repoName, orgName);
    } catch (err) {
      return;
    }

    const fuzzerArgs = {
      pathToTester,
      config: echidnaConfig,
      contract,
      corpusDir,
      forkBlock,
      forkMode,
      forkReplacement,
      rpcUrl,
      testLimit,
      testMode,
      preprocess,
      targetCorpus,
    };

    const areBasicFieldsOK = checkFields(orgName, repoName, ref);
    if (!areBasicFieldsOK) return;

    try {
      const foundData = await axios({
        method: "POST",
        url: `/api/jobs/echidna`,
        data: {
          directory,
          orgName,
          repoName,
          ref,
          fuzzerArgs,
          preprocess,
          label,
          recipeId,
        },
      });

      setJobId(foundData?.data?.data?.id as number);
      refetchJobs();
    } catch (e) {
      console.log("e", e);
      alert(`Something went wrong: ${e.response.data.message}`);
    }
  };

  const startFoundryJob = async ({
    //pathToTester,
    //echidnaConfig,
    contract,
    //corpusDir,
    forkBlock,
    forkMode,
    ref,
    repoName,
    //testLimit,
    //testMode,
    preprocess,
    directory,
    orgName,
    rpcUrl,
    runs,
    seed,
    verbosity,
    testCommand,
    testTarget,
    label,
    //targetCorpus,
  }: GitHubLinkFormValues) => {
    if (organization.billingStatus === "TRIAL" && runningJobs().length > 0) {
      alert(
        `You can only run one job at a time, currently running: ${runningJobs()
          .map((job) => `${job.label}-${job.id}`)
          .join(", ")}`
      );
      return;
    }
    try {
      await checkCanClone(repoName, orgName);
    } catch (err) {
      return;
    }

    const fuzzerArgs = {
      //pathToTester,
      //config: echidnaConfig,
      contract,
      //corpusDir,
      forkBlock,
      forkMode,
      //testLimit,
      //testMode,
      preprocess,
      //targetCorpus,
      rpcUrl,
      runs,
      seed,
      verbosity,
      testCommand,
      testTarget,
      recipeId,
    };

    const areBasicFieldsOK = checkFields(orgName, repoName, ref);
    if (!areBasicFieldsOK) return;

    try {
      const foundData = await axios({
        method: "POST",
        url: `/api/jobs/foundry`,
        data: {
          directory,
          orgName,
          repoName,
          ref,
          fuzzerArgs,
          preprocess,
          label,
        },
      });

      setJobId(foundData?.data?.data?.id as number);
      refetchJobs();
    } catch (e) {
      console.log("e", e);
      alert(`Something went wrong: ${e.response.data.message}`);
    }
  };

  const startHalmosJob = async ({
    //pathToTester,
    //echidnaConfig,
    contract,
    ref,
    repoName,
    preprocess,
    directory,
    orgName,
    halmosPrefix,
    halmosArray,
    halmosLoops,
    verbosity,
    label,
  }: GitHubLinkFormValues) => {
    if (organization.billingStatus === "TRIAL" && runningJobs().length > 0) {
      alert(
        `You can only run one job at a time, currently running: ${runningJobs()
          .map((job) => `${job.label}-${job.id}`)
          .join(", ")}`
      );
      return;
    }
    try {
      await checkCanClone(repoName, orgName);
    } catch (err) {
      return;
    }

    const fuzzerArgs = {
      contract,
      preprocess,
      halmosArray,
      halmosLoops,
      halmosPrefix,
      verbosity,
    };

    try {
      const foundData = await axios({
        method: "POST",
        url: `/api/jobs/halmos`,
        data: {
          directory,
          orgName,
          repoName,
          ref,
          fuzzerArgs,
          preprocess,
          label,
          recipeId,
        },
      });

      setJobId(foundData?.data?.data?.id as number);
      refetchJobs();
    } catch (e) {
      console.log("e", e);
      alert("Something went wrong");
    }
  };

  const startKontrolJob = async ({
    //pathToTester,
    //echidnaConfig,
    contract,
    ref,
    repoName,
    preprocess,
    directory,
    orgName,
    kontrolTest,
    label,
  }: GitHubLinkFormValues) => {
    if (organization.billingStatus === "TRIAL" && runningJobs().length > 0) {
      alert(
        `You can only run one job at a time, currently running: ${runningJobs()
          .map((job) => `${job.label}-${job.id}`)
          .join(", ")}`
      );
      return;
    }
    try {
      await checkCanClone(repoName, orgName);
    } catch (err) {
      return;
    }

    const fuzzerArgs = {
      //pathToTester,
      //config: echidnaConfig,
      contract,
      //testLimit,
      //testMode,
      preprocess,
      //targetCorpus,
      kontrolTest,
    };

    try {
      const foundData = await axios({
        method: "POST",
        url: `/api/jobs/kontrol`,
        data: {
          directory,
          orgName,
          repoName,
          ref,
          fuzzerArgs,
          preprocess,
          label,
          recipeId,
        },
      });

      setJobId(foundData?.data?.data?.id as number);
      refetchJobs();
    } catch (e) {
      console.log("e", e);
      alert("Something went wrong");
    }
  };

  const startMedusaAbiJob = async ({
    orgName,
    repoName,
    ref,
    directory,
    medusaConfig,
    timeout,
    preprocess,
    targetCorpus,
    label,
  }: GitHubLinkFormValues) => {
    if (organization.billingStatus === "TRIAL" && runningJobs().length > 0) {
      alert(
        `You can only run one job at a time, currently running: ${runningJobs()
          .map((job) => `${job.label}-${job.id}`)
          .join(", ")}`
      );
      return;
    }
    try {
      await checkCanClone(repoName, orgName);
    } catch (err) {
      return;
    }

    const fuzzerArgs = {
      timeout,
      config: medusaConfig,
      targetCorpus,
    };

    const areBasicFieldsOK = checkFields(orgName, repoName, ref);
    if (!areBasicFieldsOK) return;

    try {
      const foundData = await axios({
        method: "POST",
        url: `/api/jobs/medusa`,
        data: {
          fuzzerArgs,
          preprocess,
          orgName,
          repoName,
          ref,
          directory,
          label,
          recipeId,
        },
      });

      setJobId(foundData?.data?.data?.id as number);
      refetchJobs();
    } catch (e) {
      alert(`Something went wrong: ${e.response.data.message}`);
    }
  };

  const onSubmit =
    env === ENV_TYPE.MEDUSA
      ? startMedusaAbiJob
      : env === ENV_TYPE.ECHIDNA
        ? startEchidnaAbiJob
        : env === ENV_TYPE.FOUNDRY
          ? startFoundryJob
          : env === ENV_TYPE.HALMOS
            ? startHalmosJob
            : startKontrolJob;

  return (
    <div className="pl-[45px] pt-[45px]">
      <Toaster position="top-center" reverseOrder={false} />
      <Webhooks />
      <h1 className="mb-[16px] text-[28px] leading-[33px] text-textPrimary">
        Jobs
      </h1>
      <CreateJobForm
        title=""
        submitLabel="Run Job"
        {...{
          env,
          jobId,
          setEnv,
        }}
        onSubmit={onSubmit}
        setRecipeId={setRecipeId}
      />
      <AllJobs />
    </div>
  );
}
