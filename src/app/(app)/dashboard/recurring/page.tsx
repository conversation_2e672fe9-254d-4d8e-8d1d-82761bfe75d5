"use client";

import { AppPageTitle } from "@/app/components/app-page-title";

import { AllRecurring } from "./AllRecurring";

export default function RecurringJobs() {
  return (
    <div className="pl-[45px] pt-[45px]">
      <AppPageTitle>Recurring Jobs</AppPageTitle>

      <p className="my-[20px] text-[20px] leading-[25px] text-textSecondary">
        Below are listed your recurring jobs
      </p>
      <p className="my-[20px] text-[20px] leading-[25px] text-textSecondary">
        These are typically recurring fork tests to help ensure your smart
        contracts are safe
      </p>
      <p className="my-[20px] text-[20px] leading-[25px] text-textSecondary">
        Talk to staff to set these up!
      </p>

      <div>
        <AllRecurring />
      </div>
    </div>
  );
}
