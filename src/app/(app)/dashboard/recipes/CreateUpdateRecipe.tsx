"use client";

import axios from "axios";
import { useEffect, useState } from "react";
import { FormProvider, useForm } from "react-hook-form";

import { ENV_TYPE } from "@/app/app.constants";
import { SubFormFoundry } from "@/app/components/github-link-form/subform-foundry";
import {
  useGetRecipeByIdentifier,
  useGetRecipes,
} from "@/app/services/recipes.hook";

import { AppButton } from "../../../components/app-button";
import { AppInput } from "../../../components/app-input";
import { AppRadioGroup } from "../../../components/app-radio-group";
import { AppSpinner } from "../../../components/app-spinner";
import { SubFormEchidna } from "../../../components/github-link-form/subform-echidna";
import { SubFormMedusa } from "../../../components/github-link-form/subform-medusa";
import { SubFormHalmos } from "@/app/components/github-link-form/subform-halmos";

type GitHubLinkInputProps = {
  editId?: string;
  title?: string;
  submitLabel?: string;
  hidePresets?: boolean;
};

export type GitHubLinkFormValues = {
  // UX
  githubURL: string;

  // RECIPE:
  displayName: string;

  // Basic
  orgName: string;
  repoName: string;
  ref: string;
  directory: string;
  customOut: string;

  // Preprocess / Compatibility
  preprocess: string;
  targetCorpus: string;

  // Medusa
  medusaConfig: string;
  timeout: string;

  // Echidna
  pathToTester: string;
  echidnaConfig: string;
  forkBlock: string;
  forkMode: string;
  forkReplacement: boolean;
  contract: string;
  corpusDir: string;
  rpcUrl: string;
  testLimit: string;
  testMode: string;

  // Foundry
  runs: string;
  seed: string;
  verbosity: string;
  testCommand: string;
  testTarget: string;
  // Note that `contract`, `forkBlock` and `forkMode` is used by Echidna too

  // Halmos
  // Halmos also uses the shared `contract` and `verbosity` fields
  halmosPrefix: string;
  halmosArray: string;
  halmosLoops: string;
};

const radioOptions = [
  {
    label: "Medusa",
    value: "MEDUSA",
  },
  {
    label: "Echidna",
    value: "ECHIDNA",
  },
  {
    label: "Foundry",
    value: "FOUNDRY",
  },
  {
    label: "Halmos",
    value: "HALMOS",
  },
];

export function CreateUpdateRecipeForm({
  editId,
  title = "Create Recipe",
  submitLabel = "Create Recipe",
}: GitHubLinkInputProps) {
  const {
    register,
    handleSubmit,
    setValue,
    setError,
    watch,
    formState: { errors, isSubmitting },
  } = useForm<GitHubLinkFormValues>({
    defaultValues: {
      timeout: "3600",
    },
  });

  const { refetch: refetchRecipes } = useGetRecipes();

  // TODO Githublink form values
  const onSubmit = async ({
    orgName,
    repoName,
    ref,
    directory,
    medusaConfig,
    timeout,
    preprocess,
    displayName,

    pathToTester,
    contract,
    corpusDir,
    echidnaConfig,
    forkBlock,
    forkMode,
    forkReplacement,
    rpcUrl,
    testLimit,
    testMode,
    targetCorpus,

    runs,
    seed,
    verbosity,
    testCommand,
    testTarget,

    halmosArray,
    halmosLoops,
    halmosPrefix,
  }: GitHubLinkFormValues) => {
    const fuzzerArgs =
      env == ENV_TYPE.MEDUSA
        ? {
            timeout,
            config: medusaConfig,
            targetCorpus,
          }
        : env == ENV_TYPE.ECHIDNA
          ? {
              pathToTester,
              config: echidnaConfig,
              contract,
              corpusDir,
              forkBlock,
              forkMode,
              forkReplacement,
              rpcUrl,
              testLimit,
              testMode,
              targetCorpus,
            }
          : env == ENV_TYPE.FOUNDRY
            ? {
                contract,
                forkBlock,
                forkMode,
                rpcUrl,
                runs,
                seed,
                testCommand,
                testTarget,
                verbosity,
              }
            : {
                contract,
                testCommand,
                verbosity,
                halmosArray,
                halmosLoops,
                halmosPrefix,
              };

    // TODO: CONDITION: If we have `editId` then this is an update
    // Else it's a create

    if (editId) {
      const recipeData = {
        fuzzer: env,
        displayName,
        fuzzerArgs,
        preprocess,
        orgName,
        repoName,
        ref,
        directory,
      };

      try {
        // This is a UPDATE
        const foundData = await axios({
          method: "POST",
          url: `/api/recipes/${editId}`,
          data: {
            recipeId: editId,
            recipeData,
          },
        });
        alert("Updated recipe!");
      } catch (e) {
        console.log("e", e);
        alert(`Something went wrong: ${e.response.data.message}`);
      }
    }

    // It's a create
    if (!editId) {
      try {
        const foundData = await axios({
          method: "POST",
          url: `/api/recipes`,
          data: {
            fuzzer: env, /// @audit Necessary
            displayName,
            fuzzerArgs,
            preprocess,
            orgName,
            repoName,
            ref,
            directory,
          },
        });

        refetchRecipes();
      } catch (e) {
        console.log("e", e);
        alert(`Something went wrong: ${e.response.data.message}`);
      }
    }
  };

  const recipe = useGetRecipeByIdentifier(editId);
  const [doneLoading, setDoneLoading] = useState(false);

  // UPDATE RECIPE
  // We can pass an ID, when the ID is passed that means we're updating tge
  useEffect(() => {
    async function setPresets() {
      console.log("recipe", recipe);

      // @ts-expect-error we'd have to cast and verify but this is always valid
      setEnv(recipe.data.fuzzer);

      Object.keys(recipe.data).forEach((key) => {
        console.log("key", key);
        // @ts-expect-error we purposefully don't care, since the form will ignore extra fields anyway
        setValue(key.toString(), recipe.data[key]);
      });

      Object.keys(recipe.data?.fuzzerArgs).forEach((key) => {
        console.log("key", key);
        // @ts-expect-error See above
        setValue(key.toString(), recipe.data?.fuzzerArgs[key]);
      });
    }

    if (recipe?.data?.fuzzer && !doneLoading) {
      setPresets();
      setDoneLoading(true);
    }
  }, [recipe, doneLoading, setValue]);

  const [env, setEnv] = useState(ENV_TYPE.MEDUSA);

  const handleRadioChange = (value: string) => {
    setEnv(value as ENV_TYPE);
  };

  const parseURI = (inputValue) => {
    const success = /^(https?:\/\/)?(www\.)?github\.com\/.+\/.+(\.git)?$/.test(
      inputValue
    );

    if (!success) {
      setError("githubURL", {
        message: "Invalid GitHub URL",
      });
      return;
    }

    const ghLink = inputValue.endsWith("/")
      ? inputValue.slice(0, -1)
      : inputValue;
    const uriParts = ghLink
      .replace("https://", "")
      .replace("http://", "")
      .split("/");

    if (uriParts.length >= 3) {
      const orgName = uriParts[1];
      const repoName = uriParts[2];
      let ref = "main"; // Default branch

      // Check if the URL specifies a branch
      if (uriParts.length > 5 && uriParts[3] === "tree") {
        // The branch name can include slashes, so we join the remaining parts
        ref = uriParts.slice(4).join("/");
      } else if (uriParts.length === 5 && uriParts[3] === "tree") {
        // Handle the case where there's no slash in the branch name
        ref = uriParts[4];
      }

      // Set the values to the form
      setValue("orgName", orgName);
      setValue("repoName", repoName);
      setValue("ref", ref);
    }
  };

  const githubUrlRegister = register("githubURL");

  return (
    <FormProvider
      {...({
        register,
        handleSubmit,
        setValue,
        setError,
        watch,
        errors,
        isSubmitting,
      } as any)}
    >
      <form onSubmit={handleSubmit(onSubmit)}>
        <h3 className="my-[22px] text-[28px] leading-[33px] text-textPrimary">
          {title}
        </h3>

        <div className="mb-[22px] w-[450px]">
          <p className="mb-[9px] text-[15px] leading-[18px] text-textSecondary">
            Select Job Type
          </p>
          {env && (
            <AppRadioGroup
              name="env"
              options={radioOptions}
              onChange={handleRadioChange}
              value={env}
            />
          )}
        </div>
        <div className="flex flex-wrap gap-[40px]">
          <div className="min-w-[450px] border border-y-0 border-l-0 border-r-divider pr-[40px]">
            <div className="min-w-[400px]">
              <AppInput
                className="mb-[8px]"
                label="Recipe Display Name"
                {...register("displayName")}
                type="text"
              />
              <AppInput
                {...githubUrlRegister}
                label="Paste Github URL for your convenience"
                onChange={(e) => {
                  githubUrlRegister.onChange(e); // default react-hook-form onChange
                  parseURI(e.target.value); // additional logic for parsing URL
                }}
                type="text"
                placeholder="Enter GitHub Repo URL"
                error={errors.githubURL?.message}
              />

              <div className="my-[26px] h-[1px] w-[100%] bg-divider" />
            </div>

            <h3 className="mb-[16px] text-[16px] leading-[19px] text-textPrimary">
              Or specify the Organization, Repository and Branch directly
            </h3>

            <div className="w-[230px]">
              <AppInput
                className="mb-[8px]"
                label="Organization"
                {...register("orgName")}
                type="text"
              />

              <AppInput
                className="mb-[8px]"
                {...register("repoName")}
                type="text"
                label="Repo"
              />

              <AppInput
                className="mb-[8px]"
                {...register("ref")}
                type="text"
                label="Branch"
              />
              <AppInput
                className="mb-[8px]"
                {...register("directory")}
                type="text"
                label="Directory"
              />

              {!env && (
                <AppInput
                  className="mb-[8px]"
                  {...register("customOut")}
                  type="text"
                  label="Custom output folder"
                />
              )}
            </div>
            <AppButton
              type="submit"
              disabled={isSubmitting}
              className="mb-[20px] mt-[35px] w-[233px] justify-center"
            >
              {isSubmitting ? <AppSpinner /> : submitLabel}
            </AppButton>
          </div>

          <div className="w-[450px] border border-y-0 border-l-0 border-r-divider pr-[40px]">
            {env === ENV_TYPE.MEDUSA ? (
              <SubFormMedusa />
            ) : env === ENV_TYPE.ECHIDNA ? (
              <SubFormEchidna />
            ) : env === ENV_TYPE.FOUNDRY ? (
              <SubFormFoundry />
            ) : (
              <SubFormHalmos />
            )}
          </div>
        </div>
      </form>
    </FormProvider>
  );
}
