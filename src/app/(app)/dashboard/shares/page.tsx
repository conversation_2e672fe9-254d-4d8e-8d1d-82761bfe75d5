"use client";
import { AppCode } from "@/app/components/app-code";
import { AppPageTitle } from "@/app/components/app-page-title";
import { useGetShares } from "@/app/services/shares.hook";
import { AppButton } from "@/app/components/app-button";
import axios from "axios";
import { useMemo, useState } from "react";
import { searchObject } from "@/lib/utils";

export default function Shares() {
  const { data, refetch } = useGetShares();
  const [querry, setQuerry] = useState("");

  const deleteSharesHandler = async (shareId: string) => {
    const createdAlert = await axios({
      method: "POST",
      url: `/api/shares/delete`,
      data: {
        shareId,
      },
    });

    if (createdAlert.status === 200) {
      console.log("Shares deleted");
      refetch();
    } else {
      alert("something went wrong");
    }
  };

  const sortedShares = useMemo(() => {
    return data?.filter((shares) => {
      if (!querry) return true;

      const querryWords = querry.toLowerCase().split(/\s+/);
      return querryWords.every((word) => searchObject(shares, word));
    });
  }, [data, querry]);

  return (
    <div className="pl-[45px] pt-[45px]">
      <AppPageTitle>Shares</AppPageTitle>

      <p className="my-[20px] text-[18px] leading-[21px] text-textSecondary">
        This page lists all of the jobs you're sharing
        <br />
        You can delete a share to make the information private
      </p>
      <div className="m-auto mb-10 w-[70%]">
        <input
          type="text"
          placeholder="id, repoName, orgName, ref, label"
          className="mb-[20px] w-full rounded-[8px] border border-divider p-[10px]"
          value={querry}
          onChange={(e) => setQuerry(e.target.value)}
        />
      </div>
      <div className="flex w-full flex-col gap-[10px]">
        {sortedShares?.map((share) => (
          <div
            key={share.id}
            className="flex w-full flex-row items-center justify-center"
          >
            <AppCode
              key={share.id}
              code={JSON.stringify(share, null, 2)}
              language="json"
              extraCss="w-[80%]"
            />
            <AppButton
              onClick={() => deleteSharesHandler(share.id)}
              className="ml-6"
            >
              Delete
            </AppButton>
          </div>
        ))}
      </div>
    </div>
  );
}
