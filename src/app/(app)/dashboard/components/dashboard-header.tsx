"use client";

import { useTheme } from "next-themes";
import { <PERSON>a<PERSON><PERSON>, FaSun } from "react-icons/fa";

import { useGetMyOrg } from "@/app/services/organization.hooks";
import { THEME_OPTIONS } from "@/app/services/ThemeProvider";

import { cn } from "@/lib/utils";
import { AppLogo } from "../../../components/app-logo";
import { DashboardUser } from "./dashboard-navbar/dashboard-user";

export const DashboardHeader = () => {
  const { data: organization, isLoading, orgStatus } = useGetMyOrg();
  const { setTheme, theme: currentTheme } = useTheme();
  const isDark = currentTheme === THEME_OPTIONS.dark;

  const onThemeChange = (isDark) => {
    setTheme(!isDark ? THEME_OPTIONS.dark : THEME_OPTIONS.light);
  };

  return (
    <header className="flex w-full items-center justify-between border-b border-white/10 bg-dashboardBG px-6 py-4">
      <div className="flex items-center">
        <AppLogo />
      </div>

      <div className="ml-auto mr-2 flex min-h-[28px] min-w-[65px] cursor-pointer items-center gap-[8px] rounded-full border border-stroke-neutral-decorative bg-back-neutral-secondary px-[9px] py-[5px] text-fore-neutral-primary">
        <div
          onClick={() => onThemeChange(true)}
          className={cn("rounded-full p-[5px] transition-colors", {
            "border border-stroke-neutral-decorative bg-back-accent-primary":
              !isDark,
          })}
        >
          <FaSun className="size-3" />
        </div>
        <div
          onClick={() => onThemeChange(false)}
          className={cn("rounded-full p-[5px] transition-colors", {
            "border border-stroke-neutral-decorative bg-back-accent-primary":
              isDark,
          })}
        >
          <FaMoon className="size-3" />
        </div>
      </div>

      <DashboardUser
        {...{
          organization,
          isLoading,
          orgStatus,
        }}
      />
    </header>
  );
};
