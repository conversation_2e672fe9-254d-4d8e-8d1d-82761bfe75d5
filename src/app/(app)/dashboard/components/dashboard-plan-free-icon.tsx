export const FreeIcon = () => {
  return (
    <svg
      width="56"
      height="56"
      viewBox="0 0 56 56"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clip-path="url(#clip0_60_1469)">
        <rect width="56" height="56" rx="14" fill="#7160E8" />
        <circle
          cx="7.87262"
          cy="21.0919"
          r="41.5381"
          transform="rotate(-0.403168 7.87262 21.0919)"
          fill="url(#paint0_linear_60_1469)"
          stroke="url(#paint1_linear_60_1469)"
          stroke-width="0.085853"
        />
        <circle
          cx="28.0909"
          cy="50.775"
          r="41.5381"
          transform="rotate(-0.403168 28.0909 50.775)"
          fill="url(#paint2_linear_60_1469)"
          stroke="url(#paint3_radial_60_1469)"
          stroke-width="0.085853"
        />
        <g clip-path="url(#clip1_60_1469)">
          <path
            d="M27.9999 41.3334C27.9999 41.3334 38.6666 36.0001 38.6666 28.0001V18.6667L27.9999 14.6667L17.3333 18.6667V28.0001C17.3333 36.0001 27.9999 41.3334 27.9999 41.3334Z"
            stroke="white"
            strokeWidth="2.66667"
            strokeLinecap="round"
            stroke-linejoin="round"
          />
        </g>
      </g>
      <defs>
        <linearGradient
          id="paint0_linear_60_1469"
          x1="7.87262"
          y1="-20.4892"
          x2="7.87262"
          y2="62.673"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#5A71C6" stopOpacity="0.56" />
          <stop offset="1" stop-color="white" stop-opacity="0.2" />
        </linearGradient>
        <linearGradient
          id="paint1_linear_60_1469"
          x1="51.4246"
          y1="16.9578"
          x2="-44.9089"
          y2="38.3012"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#B5C2FB" />
          <stop offset="1" stop-color="#B5C2FB" stop-opacity="0" />
        </linearGradient>
        <linearGradient
          id="paint2_linear_60_1469"
          x1="28.0909"
          y1="9.19393"
          x2="28.0909"
          y2="92.3561"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#A7BAFF" stopOpacity="0.5" />
          <stop offset="1" stop-color="#4C5C99" stop-opacity="0.08" />
        </linearGradient>
        <radialGradient
          id="paint3_radial_60_1469"
          cx="0"
          cy="0"
          r="1"
          gradientUnits="userSpaceOnUse"
          gradientTransform="translate(28.0909 50.775) rotate(89.968) scale(86.1425)"
        >
          <stop offset="0.046875" stopColor="#B5C2FB" stopOpacity="0" />
          <stop offset="1" stopColor="#B5C2FB" />
        </radialGradient>
        <clipPath id="clip0_60_1469">
          <rect width="56" height="56" rx="14" fill="white" />
        </clipPath>
        <clipPath id="clip1_60_1469">
          <rect
            width="32"
            height="32"
            fill="white"
            transform="translate(12 12)"
          />
        </clipPath>
      </defs>
    </svg>
  );
};
